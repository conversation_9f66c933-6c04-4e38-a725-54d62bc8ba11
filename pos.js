const { ipc<PERSON>enderer } = require('electron');

// متغيرات عامة
let cart = [];
const TAX_RATE = 0.15; // 15% ضريبة القيمة المضافة

// عناصر DOM
const barcodeInput = document.getElementById('barcodeInput');
const scanButton = document.getElementById('scanButton');
const searchInput = document.getElementById('searchProduct');
const searchButton = document.getElementById('searchButton');
const searchResults = document.getElementById('searchResults');
const productList = document.getElementById('productList');
const cartItemsContainer = document.getElementById('cartItems');
const subtotalElement = document.getElementById('subtotal');
const taxElement = document.getElementById('tax');
const totalElement = document.getElementById('total');

// متغيرات للماسح الليزري
let barcodeBuffer = '';
let lastKeyTime = 0;
let barcodeTimeoutId = null;
let isManualTyping = false; // متغير لتتبع الكتابة اليدوية
const BARCODE_TIMEOUT = 50; // milliseconds between keystrokes
const MIN_BARCODE_LENGTH = 8; // الحد الأدنى لطول الباركود

// تهيئة النظام عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', async () => {
    console.log('🚀 تحميل صفحة نقطة البيع...');

    // تحميل البيانات المحفوظة
    try {
        console.log('🔄 تحميل البيانات المحفوظة...');
        const response = await ipcRenderer.invoke('load-saved-data');
        if (response.success) {
            console.log('✅ تم تحميل البيانات بنجاح');
            console.log(`📊 البيانات: ${response.data.products} منتج، ${response.data.sales} مبيعة، ${response.data.prescriptions} وصفة`);
        }
    } catch (error) {
        console.error('❌ خطأ في تحميل البيانات:', error);
    }

    initializePOS();
    setupBarcodeScanner();
    setupEventListeners();
});

function initializePOS() {
    updateCartDisplay();
    barcodeInput.focus();
    console.log('نظام نقطة البيع جاهز');
}

// إعداد الماسح الليزري
function setupBarcodeScanner() {
    // الاستماع لأحداث لوحة المفاتيح على مستوى المستند
    document.addEventListener('keydown', handleBarcodeInput);

    // إعداد حقل الباركود - منع المعالجة التلقائية أثناء الكتابة اليدوية
    barcodeInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
            e.preventDefault();
            processBarcodeInput(barcodeInput.value.trim());
        }
    });

    // منع تدخل نظام الماسح عند الكتابة في حقل الباركود
    barcodeInput.addEventListener('input', () => {
        // تحديد أن هذا إدخال يدوي
        isManualTyping = true;
        // مسح بافر الماسح عند الكتابة اليدوية
        barcodeBuffer = '';
        if (barcodeTimeoutId) {
            clearTimeout(barcodeTimeoutId);
            barcodeTimeoutId = null;
        }
    });

    // إعادة تعيين حالة الكتابة اليدوية عند فقدان التركيز
    barcodeInput.addEventListener('blur', () => {
        setTimeout(() => {
            isManualTyping = false;
        }, 100);
    });

    // زر المسح
    scanButton.addEventListener('click', () => {
        processBarcodeInput(barcodeInput.value.trim());
    });
}

// معالجة إدخال الماسح الليزري
function handleBarcodeInput(e) {
    const activeElement = document.activeElement;
    const isInputElement = activeElement.tagName === 'INPUT' || activeElement.tagName === 'TEXTAREA';
    const isBarcodeInput = activeElement === barcodeInput;
    const isSearchInput = activeElement === searchInput;

    // تجاهل تماماً إذا كنا في حقل البحث
    if (isSearchInput) {
        return;
    }

    // تجاهل إذا كنا في أي حقل نص آخر غير حقل الباركود
    if (isInputElement && !isBarcodeInput) {
        return;
    }

    const now = Date.now();
    const timeDiff = now - lastKeyTime;
    lastKeyTime = now;

    // إذا تم الضغط على Enter في حقل الباركود فقط
    if (e.key === 'Enter' && isBarcodeInput) {
        e.preventDefault();
        processBarcodeInput(barcodeInput.value.trim());
        return;
    }

    // معالجة الماسح الليزري فقط إذا لم نكن في أي حقل إدخال ولم نكن في وضع الكتابة اليدوية
    if (!isInputElement && !isManualTyping && e.key.length === 1) {
        // إذا كان الإدخال سريع (من الماسح)
        if (timeDiff < BARCODE_TIMEOUT) {
            if (barcodeTimeoutId) {
                clearTimeout(barcodeTimeoutId);
            }

            barcodeBuffer += e.key;

            // التركيز على حقل الباركود وتحديث قيمته فقط إذا كان البافر طويل بما فيه الكفاية
            if (barcodeBuffer.length >= 3) {
                barcodeInput.focus();
                barcodeInput.value = barcodeBuffer;
            }

            // تعيين مؤقت لمعالجة الباركود
            barcodeTimeoutId = setTimeout(() => {
                if (barcodeBuffer.length >= MIN_BARCODE_LENGTH) {
                    processBarcodeInput(barcodeBuffer);
                    barcodeBuffer = '';
                }
            }, BARCODE_TIMEOUT + 30);

            e.preventDefault();
        } else {
            // إدخال بطيء (كتابة يدوية) - مسح البافر
            barcodeBuffer = '';
        }
    }
}

// معالجة الباركود
async function processBarcodeInput(barcode) {
    if (!barcode) return;
    
    console.log('معالجة الباركود:', barcode);
    
    try {
        showLoading(scanButton);
        
        // البحث عن المنتج
        const product = await ipcRenderer.invoke('get-product-by-barcode', barcode);
        
        // إضافة إلى السلة
        await addToCart(product);
        
        // مسح حقل الباركود
        barcodeInput.value = '';
        barcodeBuffer = '';
        
        // إظهار رسالة نجاح
        showNotification(`تمت إضافة ${product.name}`, 'success');
        
        // صوت تأكيد
        playBeepSound();
        
        // التركيز على حقل الباركود للمسح التالي
        barcodeInput.focus();
        
    } catch (error) {
        console.error('خطأ في معالجة الباركود:', error);
        showNotification(error.message || 'حدث خطأ في قراءة الباركود', 'error');
    } finally {
        hideLoading(scanButton, '<i class="fas fa-search"></i>');
    }
}

// إعداد مستمعي الأحداث
function setupEventListeners() {
    // البحث عن المنتجات
    searchInput.addEventListener('input', handleProductSearch);
    searchButton.addEventListener('click', () => {
        handleProductSearch({ target: searchInput });
    });

    // حماية حقل البحث من تدخل نظام الماسح
    searchInput.addEventListener('focus', () => {
        // مسح بافر الماسح عند التركيز على حقل البحث
        barcodeBuffer = '';
        if (barcodeTimeoutId) {
            clearTimeout(barcodeTimeoutId);
            barcodeTimeoutId = null;
        }
    });

    searchInput.addEventListener('keydown', (e) => {
        // منع تدخل نظام الماسح في حقل البحث
        e.stopPropagation();
    });

    // زر اختبار الماسح الليزري
    document.getElementById('testBarcodeButton').addEventListener('click', testBarcodeScanner);

    // أزرار إتمام البيع والإلغاء
    document.getElementById('completeSaleBtn').addEventListener('click', () => processSale(false));
    document.getElementById('completeSaleAndPrintBtn').addEventListener('click', () => processSale(true));
    document.getElementById('cancelSaleBtn').addEventListener('click', clearCart);
}

// البحث عن المنتجات
async function handleProductSearch(e) {
    const searchTerm = e.target.value.trim();
    
    if (searchTerm.length >= 2) {
        try {
            const products = await ipcRenderer.invoke('search-products', searchTerm);
            displaySearchResults(products);
        } catch (error) {
            console.error('خطأ في البحث:', error);
        }
    } else {
        searchResults.style.display = 'none';
    }
}

// عرض نتائج البحث
function displaySearchResults(products) {
    productList.innerHTML = '';
    
    if (products.length === 0) {
        productList.innerHTML = '<div class="list-group-item text-center">لا توجد نتائج</div>';
        searchResults.style.display = 'block';
        return;
    }
    
    products.forEach(product => {
        const item = document.createElement('button');
        item.className = 'list-group-item list-group-item-action';
        item.innerHTML = `
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <strong>${product.name}</strong>
                    <small class="d-block text-muted">الكود: ${product.code}</small>
                    <small class="d-block text-muted">المتوفر: ${product.quantity}</small>
                </div>
                <div>
                    <span class="badge bg-primary rounded-pill">${product.price.toFixed(2)} ريال</span>
                </div>
            </div>
        `;
        
        item.addEventListener('click', async () => {
            try {
                await addToCart(product);
                searchInput.value = '';
                searchResults.style.display = 'none';
                showNotification(`تمت إضافة ${product.name}`, 'success');
                playBeepSound();
            } catch (error) {
                showNotification(error.message, 'error');
            }
        });
        
        productList.appendChild(item);
    });
    
    searchResults.style.display = 'block';
}

// إضافة منتج إلى السلة
async function addToCart(product) {
    try {
        cart = await ipcRenderer.invoke('add-to-cart', product);
        updateCartDisplay();
    } catch (error) {
        throw error;
    }
}

// تحديث عرض السلة
function updateCartDisplay() {
    cartItemsContainer.innerHTML = '';
    let subtotal = 0;
    
    cart.forEach(item => {
        const total = item.price * item.quantity;
        subtotal += total;
        
        const row = document.createElement('tr');
        row.innerHTML = `
            <td style="font-size: 0.85rem; padding: 0.4rem 0.5rem;">${item.name}</td>
            <td style="font-size: 0.85rem; padding: 0.4rem 0.5rem;">${getCategoryName(item.category)}</td>
            <td style="font-size: 0.85rem; padding: 0.4rem 0.5rem;">${item.price.toFixed(2)} ريال</td>
            <td style="font-size: 0.85rem; padding: 0.4rem 0.5rem;">
                <div class="input-group input-group-sm">
                    <button class="btn btn-outline-secondary btn-sm" onclick="updateQuantity(${item.id}, ${item.quantity - 1})">-</button>
                    <input type="number" class="form-control text-center" value="${item.quantity}" min="1" 
                           onchange="updateQuantity(${item.id}, this.value)" style="width: 60px;">
                    <button class="btn btn-outline-secondary btn-sm" onclick="updateQuantity(${item.id}, ${item.quantity + 1})">+</button>
                </div>
            </td>
            <td style="font-size: 0.85rem; padding: 0.4rem 0.5rem;">${total.toFixed(2)} ريال</td>
            <td style="font-size: 0.85rem; padding: 0.4rem 0.5rem;">
                <button class="btn btn-sm btn-danger" onclick="removeFromCart(${item.id})">
                    <i class="fas fa-trash-alt fa-sm"></i>
                </button>
            </td>
        `;
        cartItemsContainer.appendChild(row);
    });
    
    const tax = subtotal * TAX_RATE;
    const total = subtotal + tax;
    
    subtotalElement.textContent = `${subtotal.toFixed(2)} ريال`;
    taxElement.textContent = `${tax.toFixed(2)} ريال`;
    totalElement.textContent = `${total.toFixed(2)} ريال`;
}

// تحديث كمية المنتج - دالة عامة
window.updateQuantity = async function(productId, newQuantity) {
    try {
        const quantity = parseInt(newQuantity);
        if (quantity < 0) return;

        cart = await ipcRenderer.invoke('update-cart-quantity', productId, quantity);
        updateCartDisplay();
    } catch (error) {
        showNotification(error.message, 'error');
    }
};

// حذف منتج من السلة - دالة عامة
window.removeFromCart = async function(productId) {
    try {
        cart = await ipcRenderer.invoke('remove-from-cart', productId);
        updateCartDisplay();
        showNotification('تم حذف المنتج من السلة', 'info');
    } catch (error) {
        showNotification(error.message, 'error');
    }
};

// معالجة عملية البيع
async function processSale(shouldPrint = false) {
    if (cart.length === 0) {
        showNotification('لا توجد منتجات في السلة', 'error');
        return;
    }

    try {
        const processButton = shouldPrint ? document.getElementById('completeSaleAndPrintBtn') : document.getElementById('completeSaleBtn');
        const originalContent = shouldPrint ? '<i class="fas fa-print me-2"></i>إتمام البيع والطباعة' : '<i class="fas fa-check me-2"></i>إتمام البيع';
        showLoading(processButton);

        // الحصول على طريقة الدفع
        const paymentMethod = document.querySelector('input[name="paymentMethod"]:checked')?.value || 'cash';

        // حساب المبالغ
        const subtotal = parseFloat(subtotalElement.textContent.replace(' ريال', ''));
        const tax = parseFloat(taxElement.textContent.replace(' ريال', ''));
        const total = parseFloat(totalElement.textContent.replace(' ريال', ''));

        const saleData = {
            items: cart,
            subtotal: subtotal,
            tax: tax,
            total: total,
            paymentMethod: paymentMethod,
            date: new Date().toISOString(),
            shouldPrint: shouldPrint
        };

        const result = await ipcRenderer.invoke('process-sale', saleData);

        if (result.success) {
            // مسح السلة
            cart = [];
            updateCartDisplay();

            // إظهار رسالة نجاح
            showSuccessModal(result.saleId, saleData, shouldPrint);

            // طباعة الفاتورة إذا طُلب ذلك
            if (shouldPrint) {
                await printReceipt(result.saleId, saleData);
            }

            // صوت نجاح
            playBeepSound();

            // التركيز على حقل الباركود
            barcodeInput.focus();
        } else {
            throw new Error(result.error || 'حدث خطأ أثناء معالجة العملية');
        }
    } catch (error) {
        console.error('خطأ في معالجة البيع:', error);
        showNotification(error.message || 'حدث خطأ أثناء معالجة العملية', 'error');
    } finally {
        const processButton = shouldPrint ? document.getElementById('completeSaleAndPrintBtn') : document.getElementById('completeSaleBtn');
        const originalContent = shouldPrint ? '<i class="fas fa-print me-2"></i>إتمام البيع والطباعة' : '<i class="fas fa-check me-2"></i>إتمام البيع';
        hideLoading(processButton, originalContent);
    }
}

// مسح السلة
async function clearCart() {
    if (cart.length === 0) {
        showNotification('السلة فارغة بالفعل', 'info');
        return;
    }

    try {
        cart = await ipcRenderer.invoke('clear-cart');
        updateCartDisplay();
        showNotification('تم مسح السلة', 'info');
        barcodeInput.focus();
    } catch (error) {
        showNotification('حدث خطأ أثناء مسح السلة', 'error');
    }
}

// إظهار نافذة نجاح العملية
function showSuccessModal(saleId, saleData, wasPrinted = false) {
    const modal = document.createElement('div');
    modal.className = 'modal fade';

    const printStatus = wasPrinted ?
        '<div class="alert alert-info mt-3"><i class="fas fa-print me-2"></i>تم طباعة الفاتورة بنجاح</div>' : '';

    const printButton = !wasPrinted ?
        '<button type="button" class="btn btn-outline-primary me-2" onclick="printReceiptFromModal(\'' + saleId + '\', this)"><i class="fas fa-print me-2"></i>طباعة الفاتورة</button>' : '';

    modal.innerHTML = `
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-success text-white">
                    <h5 class="modal-title">تمت العملية بنجاح</h5>
                </div>
                <div class="modal-body text-center">
                    <i class="fas fa-check-circle text-success fa-3x mb-3"></i>
                    <h4>عملية بيع ناجحة</h4>
                    <div class="text-start mt-4">
                        <p><strong>رقم العملية:</strong> ${saleId}</p>
                        <p><strong>المبلغ الإجمالي:</strong> ${saleData.total.toFixed(2)} ريال</p>
                        <p><strong>عدد المنتجات:</strong> ${saleData.items.length}</p>
                        <p><strong>طريقة الدفع:</strong> ${saleData.paymentMethod === 'cash' ? 'نقدي' : 'بطاقة ائتمان'}</p>
                        <p><strong>التاريخ:</strong> ${new Date().toLocaleString('ar-SA')}</p>
                    </div>
                    ${printStatus}
                </div>
                <div class="modal-footer">
                    ${printButton}
                    <button type="button" class="btn btn-primary" data-bs-dismiss="modal">موافق</button>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();

    // حفظ بيانات الفاتورة للطباعة اللاحقة
    modal.saleData = saleData;

    // حذف النافذة بعد إغلاقها
    modal.addEventListener('hidden.bs.modal', () => {
        modal.remove();
    });
}

// الحصول على اسم الفئة بالعربية
function getCategoryName(category) {
    const categories = {
        'medicine': 'أدوية',
        'supplies': 'مستلزمات طبية',
        'cosmetics': 'مستحضرات تجميل'
    };
    return categories[category] || category || 'غير محدد';
}

// إظهار إشعار
function showNotification(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `toast align-items-center text-white bg-${type === 'success' ? 'success' : type === 'error' ? 'danger' : 'primary'} border-0`;
    toast.setAttribute('role', 'alert');
    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">${message}</div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
        </div>
    `;

    let toastContainer = document.querySelector('.toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.className = 'toast-container position-fixed bottom-0 end-0 p-3';
        document.body.appendChild(toastContainer);
    }

    toastContainer.appendChild(toast);
    const bsToast = new bootstrap.Toast(toast, { delay: 3000 });
    bsToast.show();

    toast.addEventListener('hidden.bs.toast', () => {
        toast.remove();
    });
}

// إظهار مؤشر التحميل
function showLoading(button) {
    button.disabled = true;
    button.innerHTML = '<span class="spinner-border spinner-border-sm" role="status"></span> جاري المعالجة...';
}

// إخفاء مؤشر التحميل
function hideLoading(button, originalContent) {
    button.disabled = false;
    button.innerHTML = originalContent;
}

// طباعة الفاتورة
async function printReceipt(saleId, saleData) {
    try {
        // إنشاء محتوى الفاتورة
        const receiptContent = generateReceiptContent(saleId, saleData);

        // طباعة باستخدام window.print()
        const printWindow = window.open('', '_blank');
        printWindow.document.write(receiptContent);
        printWindow.document.close();
        printWindow.focus();
        printWindow.print();
        printWindow.close();

        showNotification('تم إرسال الفاتورة للطباعة', 'success');

    } catch (error) {
        console.error('خطأ في الطباعة:', error);
        showNotification('حدث خطأ أثناء الطباعة', 'error');
    }
}

// طباعة الفاتورة من النافذة المنبثقة - دالة عامة
window.printReceiptFromModal = async function(saleId, button) {
    try {
        showLoading(button);

        // البحث عن النافذة المنبثقة والحصول على بيانات الفاتورة
        const modal = button.closest('.modal');
        const saleData = modal.saleData;

        if (saleData) {
            await printReceipt(saleId, saleData);
        } else {
            throw new Error('لا يمكن العثور على بيانات الفاتورة');
        }

    } catch (error) {
        console.error('خطأ في الطباعة:', error);
        showNotification('حدث خطأ أثناء الطباعة', 'error');
    } finally {
        hideLoading(button, '<i class="fas fa-print me-2"></i>طباعة الفاتورة');
    }
};

// إنشاء محتوى الفاتورة للطباعة
function generateReceiptContent(saleId, saleData) {
    const currentDate = new Date().toLocaleString('ar-SA');

    let itemsHtml = '';
    saleData.items.forEach(item => {
        itemsHtml += `
            <tr>
                <td style="text-align: right; padding: 5px; border-bottom: 1px solid #ddd;">${item.name}</td>
                <td style="text-align: center; padding: 5px; border-bottom: 1px solid #ddd;">${item.quantity}</td>
                <td style="text-align: center; padding: 5px; border-bottom: 1px solid #ddd;">${item.price.toFixed(2)}</td>
                <td style="text-align: center; padding: 5px; border-bottom: 1px solid #ddd;">${(item.price * item.quantity).toFixed(2)}</td>
            </tr>
        `;
    });

    return `
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <title>فاتورة رقم ${saleId}</title>
            <style>
                body {
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    margin: 0;
                    padding: 20px;
                    direction: rtl;
                    text-align: right;
                }
                .receipt {
                    max-width: 400px;
                    margin: 0 auto;
                    border: 1px solid #000;
                    padding: 20px;
                }
                .header {
                    text-align: center;
                    border-bottom: 2px solid #000;
                    padding-bottom: 10px;
                    margin-bottom: 20px;
                }
                .company-name {
                    font-size: 24px;
                    font-weight: bold;
                    margin-bottom: 5px;
                }
                .receipt-title {
                    font-size: 18px;
                    margin-bottom: 10px;
                }
                .info-section {
                    margin-bottom: 20px;
                }
                .info-row {
                    display: flex;
                    justify-content: space-between;
                    margin-bottom: 5px;
                }
                table {
                    width: 100%;
                    border-collapse: collapse;
                    margin-bottom: 20px;
                }
                th {
                    background-color: #f0f0f0;
                    padding: 8px;
                    border: 1px solid #000;
                    text-align: center;
                    font-weight: bold;
                }
                td {
                    padding: 5px;
                    border-bottom: 1px solid #ddd;
                    text-align: center;
                }
                .totals {
                    border-top: 2px solid #000;
                    padding-top: 10px;
                }
                .total-row {
                    display: flex;
                    justify-content: space-between;
                    margin-bottom: 5px;
                }
                .final-total {
                    font-size: 18px;
                    font-weight: bold;
                    border-top: 1px solid #000;
                    padding-top: 5px;
                }
                .footer {
                    text-align: center;
                    margin-top: 20px;
                    border-top: 1px solid #000;
                    padding-top: 10px;
                    font-size: 12px;
                }
                @media print {
                    body { margin: 0; }
                    .receipt { border: none; }
                }
            </style>
        </head>
        <body>
            <div class="receipt">
                <div class="header">
                    <div class="company-name">صيدلية النور</div>
                    <div>شارع الملك فهد، الرياض</div>
                    <div>هاتف: 0*********</div>
                    <div class="receipt-title">فاتورة بيع</div>
                </div>

                <div class="info-section">
                    <div class="info-row">
                        <span><strong>رقم الفاتورة:</strong></span>
                        <span>${saleId}</span>
                    </div>
                    <div class="info-row">
                        <span><strong>التاريخ:</strong></span>
                        <span>${currentDate}</span>
                    </div>
                    <div class="info-row">
                        <span><strong>طريقة الدفع:</strong></span>
                        <span>${saleData.paymentMethod === 'cash' ? 'نقدي' : 'بطاقة ائتمان'}</span>
                    </div>
                </div>

                <table>
                    <thead>
                        <tr>
                            <th>المنتج</th>
                            <th>الكمية</th>
                            <th>السعر</th>
                            <th>المجموع</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${itemsHtml}
                    </tbody>
                </table>

                <div class="totals">
                    <div class="total-row">
                        <span>المجموع الفرعي:</span>
                        <span>${saleData.subtotal.toFixed(2)} ريال</span>
                    </div>
                    <div class="total-row">
                        <span>ضريبة القيمة المضافة (15%):</span>
                        <span>${saleData.tax.toFixed(2)} ريال</span>
                    </div>
                    <div class="total-row final-total">
                        <span>المجموع الإجمالي:</span>
                        <span>${saleData.total.toFixed(2)} ريال</span>
                    </div>
                </div>

                <div class="footer">
                    <p>شكراً لتسوقكم معنا</p>
                    <p>الرقم الضريبي: *********</p>
                </div>
            </div>
        </body>
        </html>
    `;
}

// تشغيل صوت تأكيد
function playBeepSound() {
    const audio = new Audio();
    audio.src = 'data:audio/wav;base64,UklGRl9vT19XQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YU' +
               'tvT18AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA';
    audio.volume = 0.5;
    audio.play().catch(err => console.error('خطأ في تشغيل الصوت:', err));
}

// اختبار الماسح الليزري
function testBarcodeScanner() {
    const testBarcodes = [
        '*********012',
        '*********013',
        '*********014',
        '*********015',
        '*********016'
    ];

    const randomBarcode = testBarcodes[Math.floor(Math.random() * testBarcodes.length)];

    showNotification(`اختبار الماسح الليزري - باركود تجريبي: ${randomBarcode}`, 'info');

    // محاكاة مسح الباركود
    setTimeout(() => {
        barcodeInput.value = randomBarcode;
        processBarcodeInput(randomBarcode);
    }, 1000);
}
