// ملف اختبار البيانات
const { ipcRenderer } = require('electron');

// دالة اختبار البيانات
async function testData() {
    console.log('🧪 بدء اختبار البيانات...');
    
    try {
        // اختبار تحميل البيانات التجريبية
        console.log('1️⃣ اختبار تحميل البيانات التجريبية...');
        const forceResponse = await ipcRenderer.invoke('force-load-demo-data');
        console.log('نتيجة تحميل البيانات التجريبية:', forceResponse);
        
        // اختبار جلب المبيعات
        console.log('2️⃣ اختبار جلب المبيعات...');
        const sales = await ipcRenderer.invoke('get-sales');
        console.log('المبيعات المجلبة:', sales);
        console.log('عدد المبيعات:', sales ? sales.length : 0);
        
        // اختبار جلب المنتجات
        console.log('3️⃣ اختبار جلب المنتجات...');
        const products = await ipcRenderer.invoke('get-products');
        console.log('المنتجات المجلبة:', products);
        console.log('عدد المنتجات:', products ? products.length : 0);
        
        // اختبار جلب الوصفات
        console.log('4️⃣ اختبار جلب الوصفات...');
        const prescriptions = await ipcRenderer.invoke('get-prescriptions');
        console.log('الوصفات المجلبة:', prescriptions);
        console.log('عدد الوصفات:', prescriptions ? prescriptions.length : 0);
        
        // حساب الإجماليات
        const totalSales = sales ? sales.reduce((sum, sale) => sum + (sale.total || 0), 0) : 0;
        console.log('إجمالي المبيعات:', totalSales);
        
        // تحديث البطاقات مباشرة
        console.log('5️⃣ تحديث البطاقات مباشرة...');
        updateCardsDirect(sales, products, prescriptions);
        
        console.log('✅ انتهى اختبار البيانات');
        
    } catch (error) {
        console.error('❌ خطأ في اختبار البيانات:', error);
    }
}

// دالة تحديث البطاقات مباشرة
function updateCardsDirect(sales, products, prescriptions) {
    // إجمالي المبيعات
    const totalSalesElement = document.getElementById('totalSales');
    if (totalSalesElement) {
        const totalSales = sales ? sales.reduce((sum, sale) => sum + (sale.total || 0), 0) : 0;
        totalSalesElement.textContent = totalSales.toFixed(2);
        console.log('✅ تم تحديث إجمالي المبيعات:', totalSales.toFixed(2));
    }
    
    // عدد المعاملات
    const transactionCountElement = document.getElementById('transactionCount');
    if (transactionCountElement) {
        const count = sales ? sales.length : 0;
        transactionCountElement.textContent = count;
        console.log('✅ تم تحديث عدد المعاملات:', count);
    }
    
    // عدد الوصفات
    const prescriptionCountElement = document.getElementById('prescriptionCount');
    if (prescriptionCountElement) {
        const count = prescriptions ? prescriptions.length : 0;
        prescriptionCountElement.textContent = count;
        console.log('✅ تم تحديث عدد الوصفات:', count);
    }
    
    // المنتجات منخفضة المخزون
    const lowStockCountElement = document.getElementById('lowStockCount');
    if (lowStockCountElement) {
        const lowStockProducts = products ? products.filter(product => product.quantity < 10) : [];
        lowStockCountElement.textContent = lowStockProducts.length;
        console.log('✅ تم تحديث المنتجات منخفضة المخزون:', lowStockProducts.length);
    }
}

// تشغيل الاختبار عند تحميل الصفحة
if (typeof document !== 'undefined') {
    document.addEventListener('DOMContentLoaded', () => {
        // إضافة زر اختبار
        const testButton = document.createElement('button');
        testButton.textContent = '🧪 اختبار البيانات';
        testButton.className = 'btn btn-warning btn-sm';
        testButton.style.position = 'fixed';
        testButton.style.top = '10px';
        testButton.style.left = '10px';
        testButton.style.zIndex = '9999';
        testButton.onclick = testData;
        document.body.appendChild(testButton);
        
        // تشغيل الاختبار تلقائياً بعد 3 ثوان
        setTimeout(testData, 3000);
    });
}

// تصدير الدالة للاستخدام الخارجي
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { testData, updateCardsDirect };
}
