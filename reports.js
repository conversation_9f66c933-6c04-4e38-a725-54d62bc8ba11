const { ipc<PERSON><PERSON><PERSON> } = require('electron');

// متغيرات عامة
let salesData = [];
let productsData = [];
let prescriptionsData = [];

// متغيرات الرسومات البيانية الجديدة
let dailySalesChart = null;
let categorySalesChart = null;

// عناصر DOM
const startDateInput = document.getElementById('startDate');
const endDateInput = document.getElementById('endDate');
const updateReportsBtn = document.getElementById('updateReports');
const autoUpdateToggle = document.getElementById('autoUpdateToggle');
const resetDataBtn = document.getElementById('resetDataBtn');
const loadDemoDataBtn = document.getElementById('loadDemoDataBtn');

// عناصر الإحصائيات
const totalSalesElement = document.getElementById('totalSales');
const transactionCountElement = document.getElementById('transactionCount');
const prescriptionCountElement = document.getElementById('prescriptionCount');
const lowStockCountElement = document.getElementById('lowStockCount');
const recentSalesTable = document.getElementById('recentSalesTable');
const noSalesMessage = document.getElementById('noSalesMessage');

// عناصر حالة الاتصال
const connectionStatusElement = document.getElementById('connectionStatus');
const lastUpdateTimeElement = document.getElementById('lastUpdateTimeValue');

// تحميل البيانات عند بدء الصفحة
document.addEventListener('DOMContentLoaded', async () => {
    console.log('🚀 تحميل صفحة التقارير...');

    // تحميل البيانات المحفوظة
    try {
        console.log('🔄 تحميل البيانات المحفوظة...');
        const response = await ipcRenderer.invoke('load-saved-data');
        if (response.success) {
            console.log('✅ تم تحميل البيانات بنجاح');
            console.log('📊 البيانات المحملة:', response.data);
        }
    } catch (error) {
        console.error('❌ خطأ في تحميل البيانات:', error);
    }

    // تأخير قصير للتأكد من حفظ البيانات
    await new Promise(resolve => setTimeout(resolve, 500));

    await initializeReports();
    setupEventListeners();
    setDefaultDateRange();

    // تحديث إضافي بعد التهيئة
    setTimeout(async () => {
        console.log('🔄 تحديث إضافي للتأكد من عرض البيانات...');
        await loadAllData();
        updateStatistics();
        await reloadCharts(); // استخدام النظام الجديد
        updateRecentSalesTable();

        // اختبار البيانات مباشرة
        console.log('🧪 اختبار البيانات المحملة:');
        console.log('salesData:', salesData);
        console.log('productsData:', productsData);
        console.log('prescriptionsData:', prescriptionsData);

        // تحديث العناصر مباشرة إذا لم تُحدث
        if (totalSalesElement && totalSalesElement.textContent === '0.00') {
            console.log('⚠️ البطاقات لا تزال فارغة - تحديث مباشر...');
            const totalSales = (salesData || []).reduce((sum, sale) => sum + (sale.total || 0), 0);
            totalSalesElement.textContent = totalSales.toFixed(2);

            if (transactionCountElement) {
                transactionCountElement.textContent = (salesData || []).length;
            }

            if (prescriptionCountElement) {
                prescriptionCountElement.textContent = (prescriptionsData || []).length;
            }

            if (lowStockCountElement) {
                const lowStockProducts = (productsData || []).filter(product => product.quantity < 10);
                lowStockCountElement.textContent = lowStockProducts.length;
            }

            console.log('✅ تم التحديث المباشر للبطاقات');
        }
    }, 1000);
});

// إعداد مستمعي الأحداث
function setupEventListeners() {
    // تحديث التقارير
    if (updateReportsBtn) {
        updateReportsBtn.addEventListener('click', updateReports);
    }
    
    // تغيير التواريخ
    if (startDateInput) {
        startDateInput.addEventListener('change', () => {
            if (autoUpdateToggle.checked) {
                updateReports();
            }
        });
    }
    
    if (endDateInput) {
        endDateInput.addEventListener('change', () => {
            if (autoUpdateToggle.checked) {
                updateReports();
            }
        });
    }
    
    // مسح البيانات
    if (resetDataBtn) {
        resetDataBtn.addEventListener('click', resetAllData);
    }

    // تحميل البيانات التجريبية
    if (loadDemoDataBtn) {
        loadDemoDataBtn.addEventListener('click', loadDemoData);
    }
}

// تعيين نطاق التواريخ الافتراضي
function setDefaultDateRange() {
    const today = new Date();
    const lastMonth = new Date(today.getFullYear(), today.getMonth() - 1, today.getDate());
    
    if (startDateInput) {
        startDateInput.value = lastMonth.toISOString().split('T')[0];
    }
    
    if (endDateInput) {
        endDateInput.value = today.toISOString().split('T')[0];
    }
}

// تهيئة التقارير
async function initializeReports() {
    try {
        console.log('🚀 بدء تهيئة التقارير...');

        // تحميل البيانات مع معالجة الأخطاء
        await loadAllData();

        console.log('📊 تم تحميل البيانات:', {
            sales: salesData ? salesData.length : 0,
            products: productsData ? productsData.length : 0,
            prescriptions: prescriptionsData ? prescriptionsData.length : 0,
            totalSalesValue: salesData ? salesData.reduce((sum, sale) => sum + (sale.total || 0), 0) : 0
        });

        // تحديث جميع عناصر التقارير
        updateStatistics();
        createCharts(); // استخدام النظام الجديد
        updateRecentSalesTable();
        updateLastUpdateTime();
        updateConnectionStatus('connected');

        console.log('✅ تم تحميل التقارير بنجاح');

        // إضافة تأثير بصري لإظهار أن التحميل اكتمل
        const loadingElements = document.querySelectorAll('.loading');
        loadingElements.forEach(el => el.style.display = 'none');

    } catch (error) {
        console.error('❌ خطأ في تهيئة التقارير:', error);
        updateConnectionStatus('error');
        showAlert('فشل في تحميل التقارير: ' + error.message, 'error');

        // إنشاء رسومات فارغة كحل بديل
        try {
            console.log('🔄 إنشاء رسومات فارغة...');
            createCharts(); // ستنشئ رسومات فارغة تلقائياً
            updateStatistics();
            updateRecentSalesTable();
            updateLastUpdateTime();
            updateConnectionStatus('connected');
            console.log('✅ تم إنشاء رسومات فارغة - جاهزة لاستقبال البيانات');
        } catch (fallbackError) {
            console.error('❌ فشل في إنشاء الرسومات الفارغة:', fallbackError);
        }
    }
}

// تحميل جميع البيانات
async function loadAllData() {
    try {
        console.log('🔄 بدء تحميل البيانات...');

        // تحميل المبيعات
        salesData = await ipcRenderer.invoke('get-sales') || [];
        console.log('📊 المبيعات المحملة:', salesData.length, salesData);

        // تحميل المنتجات
        productsData = await ipcRenderer.invoke('get-products') || [];
        console.log('📦 المنتجات المحملة:', productsData.length, productsData);

        // تحميل الوصفات
        prescriptionsData = await ipcRenderer.invoke('get-prescriptions') || [];
        console.log('💊 الوصفات المحملة:', prescriptionsData.length, prescriptionsData);

        // لا حاجة للتحقق من البيانات الفارغة - هذا طبيعي في البداية

        console.log('✅ تم تحميل البيانات النهائية:', {
            sales: salesData.length,
            products: productsData.length,
            prescriptions: prescriptionsData.length,
            totalSalesValue: salesData.reduce((sum, sale) => sum + (sale.total || 0), 0)
        });
    } catch (error) {
        console.error('❌ خطأ في تحميل البيانات:', error);
        // تعيين قيم افتراضية في حالة الخطأ
        salesData = [];
        productsData = [];
        prescriptionsData = [];
    }
}

// تحديث التقارير
async function updateReports() {
    try {
        showLoading(true);
        updateConnectionStatus('updating');

        console.log('🔄 بدء تحديث التقارير...');

        // إعادة تحميل البيانات
        await loadAllData();

        // تحديث جميع العناصر
        updateStatisticsEnhanced();
        await updateChartsWithData(); // استخدام النظام الجديد
        updateRecentSalesTable();
        updateLastUpdateTime();

        updateConnectionStatus('connected');
        showAlert('تم تحديث التقارير بنجاح', 'success');
        console.log('✅ تم تحديث التقارير بنجاح');

    } catch (error) {
        console.error('❌ خطأ في تحديث التقارير:', error);
        updateConnectionStatus('error');
        showAlert('فشل في تحديث التقارير: ' + error.message, 'error');

        // محاولة تحميل البيانات التجريبية كحل بديل
        try {
            console.log('🔄 إنشاء رسومات فارغة كحل بديل...');
            await reloadCharts(); // ستنشئ رسومات فارغة تلقائياً
            updateStatisticsEnhanced();
            updateRecentSalesTable();
            updateLastUpdateTime();
            updateConnectionStatus('connected');
            showAlert('تم إنشاء رسومات فارغة - جاهزة لاستقبال البيانات', 'info');
        } catch (fallbackError) {
            console.error('❌ فشل في إنشاء الرسومات الفارغة:', fallbackError);
        }

    } finally {
        showLoading(false);
    }
}

// تحديث الإحصائيات
function updateStatistics() {
    console.log('🔄 تحديث الإحصائيات...');
    console.log('البيانات المتاحة:', {
        salesData: salesData ? salesData.length : 0,
        productsData: productsData ? productsData.length : 0,
        prescriptionsData: prescriptionsData ? prescriptionsData.length : 0
    });

    const dateRange = getDateRange();
    const filteredSales = filterSalesByDate(salesData || [], dateRange);

    console.log('المبيعات المفلترة:', filteredSales.length, filteredSales);

    // إجمالي المبيعات
    const totalSales = filteredSales.reduce((sum, sale) => sum + (sale.total || 0), 0);
    console.log('إجمالي المبيعات:', totalSales);
    if (totalSalesElement) {
        totalSalesElement.textContent = totalSales.toFixed(2);
        console.log('✅ تم تحديث إجمالي المبيعات:', totalSales.toFixed(2));
    }

    // عدد المعاملات
    if (transactionCountElement) {
        transactionCountElement.textContent = filteredSales.length;
        console.log('✅ تم تحديث عدد المعاملات:', filteredSales.length);
    }

    // عدد الوصفات
    const filteredPrescriptions = filterPrescriptionsByDate(prescriptionsData || [], dateRange);
    console.log('الوصفات المفلترة:', filteredPrescriptions.length);
    if (prescriptionCountElement) {
        prescriptionCountElement.textContent = filteredPrescriptions.length;
        console.log('✅ تم تحديث عدد الوصفات:', filteredPrescriptions.length);
    }

    // المنتجات منخفضة المخزون (أقل من 10)
    const lowStockProducts = (productsData || []).filter(product => product.quantity < 10);
    console.log('المنتجات منخفضة المخزون:', lowStockProducts.length);
    if (lowStockCountElement) {
        lowStockCountElement.textContent = lowStockProducts.length;
        console.log('✅ تم تحديث المنتجات منخفضة المخزون:', lowStockProducts.length);
    }

    // تحديث رسالة عدم وجود بيانات
    updateNoDataMessage();

    console.log('✅ تم الانتهاء من تحديث الإحصائيات');
}

// دالة لتحديث رسالة عدم وجود بيانات
function updateNoDataMessage() {
    const hasData = (salesData && salesData.length > 0) ||
                   (prescriptionsData && prescriptionsData.length > 0);

    if (noSalesMessage) {
        if (hasData) {
            noSalesMessage.style.display = 'none';
        } else {
            noSalesMessage.style.display = 'block';
            noSalesMessage.innerHTML = `
                <div class="text-center py-5">
                    <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد بيانات لعرضها حالياً</h5>
                    <p class="text-muted">ستظهر البيانات هنا بمجرد إضافة مبيعات أو وصفات طبية</p>
                    <small class="text-muted">
                        <i class="fas fa-info-circle me-1"></i>
                        البيانات مربوطة تلقائياً مع نقطة البيع والوصفات الطبية
                    </small>
                </div>
            `;
        }
    }
}

// === نظام الرسومات البيانية الجديد ===

// دالة تحضير بيانات المبيعات للرسومات البيانية
function prepareSalesDataForCharts() {
    console.log('📊 تحضير بيانات المبيعات للرسومات البيانية...');

    if (!salesData || !Array.isArray(salesData) || salesData.length === 0) {
        console.log('⚠️ لا توجد بيانات مبيعات');
        return null;
    }

    // فلترة البيانات حسب النطاق الزمني المحدد
    const dateRange = getDateRange();
    const filteredSales = filterSalesByDate(salesData, dateRange);

    console.log(`📊 تم فلترة ${filteredSales.length} عملية بيع من أصل ${salesData.length}`);

    if (filteredSales.length === 0) {
        console.log('⚠️ لا توجد مبيعات في النطاق الزمني المحدد');
        return null;
    }

    return filteredSales;
}

// دالة تجميع المبيعات حسب التاريخ
function groupSalesByDate(salesData) {
    const salesByDate = {};

    salesData.forEach(sale => {
        try {
            // استخدام timestamp أو date
            const saleDate = new Date(sale.timestamp || sale.date);

            // تنسيق التاريخ
            const dateKey = saleDate.toLocaleDateString('ar-SA', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit'
            });

            // تجميع المبيعات
            if (!salesByDate[dateKey]) {
                salesByDate[dateKey] = {
                    total: 0,
                    count: 0,
                    items: []
                };
            }

            salesByDate[dateKey].total += (sale.total || 0);
            salesByDate[dateKey].count += 1;

            // إضافة تفاصيل العناصر
            if (sale.items && Array.isArray(sale.items)) {
                salesByDate[dateKey].items.push(...sale.items);
            }

        } catch (error) {
            console.error('خطأ في معالجة تاريخ البيع:', sale, error);
        }
    });

    return salesByDate;
}

// دالة تجميع المبيعات حسب الفئة
function groupSalesByCategory(salesData) {
    const salesByCategory = {};

    salesData.forEach(sale => {
        if (sale.items && Array.isArray(sale.items)) {
            sale.items.forEach(item => {
                try {
                    // البحث عن المنتج للحصول على الفئة
                    let categoryKey = 'غير محدد';
                    let categoryName = 'غير محدد';

                    // البحث في بيانات المنتجات أولاً
                    const product = productsData.find(p => p.id === item.id);
                    if (product && product.category) {
                        categoryKey = product.category;
                        categoryName = getCategoryName(product.category);
                    } else if (item.category) {
                        // استخدام فئة العنصر إذا لم نجد المنتج
                        categoryKey = item.category;
                        categoryName = getCategoryName(item.category);
                    }

                    // تجميع المبيعات
                    if (!salesByCategory[categoryKey]) {
                        salesByCategory[categoryKey] = {
                            name: categoryName,
                            total: 0,
                            quantity: 0,
                            items: []
                        };
                    }

                    const itemTotal = (item.price || 0) * (item.quantity || 0);
                    salesByCategory[categoryKey].total += itemTotal;
                    salesByCategory[categoryKey].quantity += (item.quantity || 0);
                    salesByCategory[categoryKey].items.push({
                        name: item.name,
                        quantity: item.quantity,
                        total: itemTotal
                    });

                } catch (error) {
                    console.error('خطأ في معالجة عنصر المبيعات:', item, error);
                }
            });
        }
    });

    return salesByCategory;
}

// دالة إنشاء رسم المبيعات اليومية الجديد
function createDailySalesChart() {
    console.log('📊 إنشاء رسم المبيعات اليومية الجديد...');

    const canvas = document.getElementById('salesChart');
    if (!canvas) {
        console.error('❌ عنصر salesChart غير موجود');
        return;
    }

    // تدمير الرسم السابق
    if (dailySalesChart) {
        dailySalesChart.destroy();
        dailySalesChart = null;
    }

    // تحضير البيانات
    const filteredSales = prepareSalesDataForCharts();
    if (!filteredSales) {
        // إنشاء رسم فارغ
        createEmptyDailySalesChart(canvas);
        return;
    }

    // تجميع البيانات حسب التاريخ
    const salesByDate = groupSalesByDate(filteredSales);

    // تحويل البيانات لتنسيق Chart.js
    const sortedDates = Object.keys(salesByDate).sort((a, b) => new Date(a) - new Date(b));
    const chartLabels = sortedDates;
    const chartData = sortedDates.map(date => salesByDate[date].total);

    console.log('📊 تسميات الرسم:', chartLabels);
    console.log('📊 بيانات الرسم:', chartData);

    // إنشاء الرسم
    const ctx = canvas.getContext('2d');
    dailySalesChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: chartLabels,
            datasets: [{
                label: 'إجمالي المبيعات (ر.س)',
                data: chartData,
                borderColor: '#007bff',
                backgroundColor: 'rgba(0, 123, 255, 0.1)',
                borderWidth: 3,
                pointBackgroundColor: '#007bff',
                pointBorderColor: '#ffffff',
                pointBorderWidth: 2,
                pointRadius: 6,
                pointHoverRadius: 8,
                fill: true,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: 'المبيعات اليومية',
                    font: {
                        size: 16,
                        weight: 'bold'
                    }
                },
                legend: {
                    display: true,
                    position: 'top'
                },
                tooltip: {
                    mode: 'index',
                    intersect: false,
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: '#ffffff',
                    bodyColor: '#ffffff',
                    borderColor: '#007bff',
                    borderWidth: 1,
                    callbacks: {
                        title: function(context) {
                            return 'التاريخ: ' + context[0].label;
                        },
                        label: function(context) {
                            const date = context.label;
                            const total = context.parsed.y;
                            const count = salesByDate[date].count;
                            return [
                                `المبلغ: ${total.toFixed(2)} ر.س`,
                                `عدد المعاملات: ${count}`
                            ];
                        }
                    }
                }
            },
            scales: {
                x: {
                    display: true,
                    title: {
                        display: true,
                        text: 'التاريخ',
                        font: {
                            size: 14,
                            weight: 'bold'
                        }
                    },
                    grid: {
                        display: true,
                        color: 'rgba(0, 0, 0, 0.1)'
                    }
                },
                y: {
                    display: true,
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'المبلغ (ر.س)',
                        font: {
                            size: 14,
                            weight: 'bold'
                        }
                    },
                    grid: {
                        display: true,
                        color: 'rgba(0, 0, 0, 0.1)'
                    },
                    ticks: {
                        callback: function(value) {
                            return value.toFixed(2) + ' ر.س';
                        }
                    }
                }
            },
            interaction: {
                mode: 'nearest',
                axis: 'x',
                intersect: false
            },
            animation: {
                duration: 1000,
                easing: 'easeInOutQuart'
            }
        }
    });

    console.log('✅ تم إنشاء رسم المبيعات اليومية بنجاح');
}

// دالة إنشاء رسم فارغ للمبيعات اليومية
function createEmptyDailySalesChart(canvas) {
    console.log('📊 إنشاء رسم مبيعات يومية فارغ...');

    const ctx = canvas.getContext('2d');
    dailySalesChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: ['لا توجد بيانات'],
            datasets: [{
                label: 'المبيعات اليومية',
                data: [0],
                borderColor: '#dee2e6',
                backgroundColor: 'rgba(222, 226, 230, 0.1)',
                borderWidth: 2,
                pointRadius: 0,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: 'المبيعات اليومية - لا توجد بيانات',
                    font: {
                        size: 16,
                        weight: 'bold'
                    },
                    color: '#6c757d'
                },
                legend: {
                    display: false
                },
                tooltip: {
                    enabled: false
                }
            },
            scales: {
                x: {
                    display: true,
                    title: {
                        display: true,
                        text: 'التاريخ',
                        color: '#6c757d'
                    }
                },
                y: {
                    display: true,
                    beginAtZero: true,
                    max: 100,
                    title: {
                        display: true,
                        text: 'المبلغ (ر.س)',
                        color: '#6c757d'
                    },
                    ticks: {
                        callback: function(value) {
                            return value + ' ر.س';
                        },
                        color: '#6c757d'
                    }
                }
            }
        }
    });
}

// دالة إنشاء رسم توزيع المبيعات حسب الفئة الجديد
function createCategorySalesChart() {
    console.log('📊 إنشاء رسم توزيع المبيعات حسب الفئة الجديد...');

    const canvas = document.getElementById('categoryChart');
    if (!canvas) {
        console.error('❌ عنصر categoryChart غير موجود');
        return;
    }

    // تدمير الرسم السابق
    if (categorySalesChart) {
        categorySalesChart.destroy();
        categorySalesChart = null;
    }

    // تحضير البيانات
    const filteredSales = prepareSalesDataForCharts();
    if (!filteredSales) {
        // إنشاء رسم فارغ
        createEmptyCategorySalesChart(canvas);
        return;
    }

    // تجميع البيانات حسب الفئة
    const salesByCategory = groupSalesByCategory(filteredSales);

    // تحويل البيانات لتنسيق Chart.js
    const categories = Object.keys(salesByCategory);
    if (categories.length === 0) {
        createEmptyCategorySalesChart(canvas);
        return;
    }

    const chartLabels = categories.map(key => salesByCategory[key].name);
    const chartData = categories.map(key => salesByCategory[key].total);
    const chartQuantities = categories.map(key => salesByCategory[key].quantity);

    console.log('📊 فئات المبيعات:', chartLabels);
    console.log('📊 قيم الفئات:', chartData);
    console.log('📊 كميات الفئات:', chartQuantities);

    // ألوان متنوعة للفئات
    const colors = [
        '#FF6384', // أحمر وردي
        '#36A2EB', // أزرق
        '#FFCE56', // أصفر
        '#4BC0C0', // تركوازي
        '#9966FF', // بنفسجي
        '#FF9F40', // برتقالي
        '#FF6B6B', // أحمر فاتح
        '#4ECDC4', // أخضر مائي
        '#45B7D1', // أزرق فاتح
        '#96CEB4', // أخضر فاتح
        '#FFEAA7', // أصفر فاتح
        '#DDA0DD'  // بنفسجي فاتح
    ];

    // إنشاء الرسم
    const ctx = canvas.getContext('2d');
    categorySalesChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: chartLabels,
            datasets: [{
                data: chartData,
                backgroundColor: colors.slice(0, chartLabels.length),
                borderColor: '#ffffff',
                borderWidth: 3,
                hoverBorderWidth: 4,
                hoverBorderColor: '#ffffff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: 'توزيع المبيعات حسب الفئة',
                    font: {
                        size: 16,
                        weight: 'bold'
                    }
                },
                legend: {
                    display: true,
                    position: 'bottom',
                    labels: {
                        padding: 20,
                        usePointStyle: true,
                        font: {
                            size: 12
                        },
                        generateLabels: function(chart) {
                            const data = chart.data;
                            if (data.labels.length && data.datasets.length) {
                                return data.labels.map((label, i) => {
                                    const value = data.datasets[0].data[i];
                                    const total = data.datasets[0].data.reduce((a, b) => a + b, 0);
                                    const percentage = ((value / total) * 100).toFixed(1);
                                    return {
                                        text: `${label} (${percentage}%)`,
                                        fillStyle: data.datasets[0].backgroundColor[i],
                                        strokeStyle: data.datasets[0].borderColor,
                                        lineWidth: data.datasets[0].borderWidth,
                                        hidden: false,
                                        index: i
                                    };
                                });
                            }
                            return [];
                        }
                    }
                },
                tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: '#ffffff',
                    bodyColor: '#ffffff',
                    borderColor: '#007bff',
                    borderWidth: 1,
                    callbacks: {
                        title: function(context) {
                            return context[0].label;
                        },
                        label: function(context) {
                            const categoryKey = categories[context.dataIndex];
                            const categoryData = salesByCategory[categoryKey];
                            const value = context.parsed;
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = ((value / total) * 100).toFixed(1);

                            return [
                                `المبلغ: ${value.toFixed(2)} ر.س`,
                                `النسبة: ${percentage}%`,
                                `الكمية: ${categoryData.quantity} قطعة`,
                                `عدد الأصناف: ${categoryData.items.length}`
                            ];
                        }
                    }
                }
            },
            cutout: '50%',
            animation: {
                animateRotate: true,
                animateScale: true,
                duration: 1000,
                easing: 'easeInOutQuart'
            },
            interaction: {
                intersect: false
            }
        }
    });

    console.log('✅ تم إنشاء رسم توزيع المبيعات حسب الفئة بنجاح');
}

// دالة إنشاء رسم فارغ لتوزيع الفئات
function createEmptyCategorySalesChart(canvas) {
    console.log('📊 إنشاء رسم توزيع فئات فارغ...');

    const ctx = canvas.getContext('2d');
    categorySalesChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['لا توجد بيانات'],
            datasets: [{
                data: [1],
                backgroundColor: ['#dee2e6'],
                borderColor: '#ffffff',
                borderWidth: 2
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: 'توزيع المبيعات حسب الفئة - لا توجد بيانات',
                    font: {
                        size: 16,
                        weight: 'bold'
                    },
                    color: '#6c757d'
                },
                legend: {
                    display: false
                },
                tooltip: {
                    enabled: false
                }
            },
            cutout: '50%'
        }
    });
}

// الدالة الرئيسية لإنشاء جميع الرسومات البيانية
function createCharts() {
    console.log('🚀 بدء إنشاء جميع الرسومات البيانية...');

    try {
        // التحقق من وجود البيانات
        console.log('📊 حالة البيانات:', {
            sales: salesData ? salesData.length : 0,
            products: productsData ? productsData.length : 0,
            prescriptions: prescriptionsData ? prescriptionsData.length : 0
        });

        // إنشاء الرسومات
        createDailySalesChart();
        createCategorySalesChart();

        console.log('✅ تم إنشاء جميع الرسومات البيانية بنجاح');

    } catch (error) {
        console.error('❌ خطأ في إنشاء الرسومات البيانية:', error);

        // محاولة إنشاء رسومات فارغة في حالة الخطأ
        try {
            const salesCanvas = document.getElementById('salesChart');
            const categoryCanvas = document.getElementById('categoryChart');

            if (salesCanvas) createEmptyDailySalesChart(salesCanvas);
            if (categoryCanvas) createEmptyCategorySalesChart(categoryCanvas);

        } catch (fallbackError) {
            console.error('❌ خطأ في إنشاء الرسومات الفارغة:', fallbackError);
        }
    }
}

// دالة تحديث الرسومات البيانية
function updateCharts() {
    console.log('🔄 تحديث الرسومات البيانية...');
    createCharts();
}

// دالة تحديث الرسومات مع إعادة تحميل البيانات
async function updateChartsWithData() {
    console.log('🔄 تحديث الرسومات مع إعادة تحميل البيانات...');

    try {
        // إعادة تحميل البيانات أولاً
        await loadAllData();

        // ثم تحديث الرسومات
        createCharts();

        console.log('✅ تم تحديث الرسومات مع البيانات الجديدة');

    } catch (error) {
        console.error('❌ خطأ في تحديث الرسومات مع البيانات:', error);
    }
}

// دالة تدمير جميع الرسومات البيانية
function destroyAllCharts() {
    console.log('🗑️ تدمير جميع الرسومات البيانية...');

    if (dailySalesChart) {
        dailySalesChart.destroy();
        dailySalesChart = null;
    }

    if (categorySalesChart) {
        categorySalesChart.destroy();
        categorySalesChart = null;
    }

    console.log('✅ تم تدمير جميع الرسومات البيانية');
}

// دالة إعادة تحميل الرسومات البيانية بالكامل
async function reloadCharts() {
    console.log('🔄 إعادة تحميل الرسومات البيانية بالكامل...');

    try {
        // تدمير الرسومات الحالية
        destroyAllCharts();

        // إعادة تحميل البيانات
        await loadAllData();

        // إنشاء الرسومات من جديد
        createCharts();

        console.log('✅ تم إعادة تحميل الرسومات البيانية بنجاح');

    } catch (error) {
        console.error('❌ خطأ في إعادة تحميل الرسومات البيانية:', error);
    }
}



// تحديث جدول المبيعات الأخيرة
function updateRecentSalesTable() {
    console.log('📋 تحديث جدول المبيعات الأخيرة...');

    if (!recentSalesTable) {
        console.error('❌ عنصر recentSalesTable غير موجود');
        return;
    }

    console.log('📋 البيانات المتاحة:', {
        salesData: salesData ? salesData.length : 0,
        productsData: productsData ? productsData.length : 0
    });

    // عرض جميع المبيعات بدون تصفية تاريخ أولاً للاختبار
    const allSales = salesData || [];
    console.log('📋 جميع المبيعات:', allSales);

    if (allSales.length === 0) {
        console.log('⚠️ لا توجد مبيعات لعرضها');
        recentSalesTable.innerHTML = `
            <tr>
                <td colspan="4" class="text-center text-muted py-4">
                    <i class="fas fa-shopping-cart fa-2x mb-2"></i><br>
                    لا توجد مبيعات لعرضها حالياً<br>
                    <small>ستظهر المبيعات هنا بمجرد إتمام عمليات البيع</small>
                </td>
            </tr>
        `;
        return;
    }

    // أخذ آخر 10 مبيعات
    const recentSales = allSales.slice(-10).reverse();
    console.log('📋 المبيعات الأخيرة للعرض:', recentSales);

    try {
        const tableHTML = recentSales.map(sale => {
            console.log('📋 معالجة مبيعة:', sale);

            // التأكد من وجود البيانات المطلوبة
            const saleId = sale.id || 'غير محدد';
            const saleDate = sale.timestamp || sale.date || new Date().toISOString();
            const saleItems = sale.items || [];
            const saleTotal = sale.total || 0;

            // تنسيق المنتجات
            const itemsText = saleItems.length > 0
                ? saleItems.map(item => {
                    const product = productsData.find(p => p.id === item.id);
                    const productName = product ? product.name : `منتج #${item.id}`;
                    return `${productName} (${item.quantity || 1})`;
                }).join(', ')
                : 'لا توجد منتجات';

            return `
                <tr>
                    <td><strong>#${saleId}</strong></td>
                    <td>${formatDateTime(saleDate)}</td>
                    <td>
                        <small>${itemsText}</small>
                    </td>
                    <td><strong>${saleTotal.toFixed(2)} ر.س</strong></td>
                </tr>
            `;
        }).join('');

        recentSalesTable.innerHTML = tableHTML;
        console.log('✅ تم تحديث جدول المبيعات بنجاح');

    } catch (error) {
        console.error('❌ خطأ في تحديث جدول المبيعات:', error);
        recentSalesTable.innerHTML = `
            <tr>
                <td colspan="4" class="text-center text-danger py-4">
                    <i class="fas fa-exclamation-triangle fa-2x mb-2"></i><br>
                    خطأ في تحميل بيانات المبيعات<br>
                    <small>يرجى المحاولة مرة أخرى</small>
                </td>
            </tr>
        `;
    }
}



// الحصول على نطاق التواريخ
function getDateRange() {
    const startDate = startDateInput ? new Date(startDateInput.value) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    const endDate = endDateInput ? new Date(endDateInput.value) : new Date();

    // تعيين الوقت لبداية ونهاية اليوم
    startDate.setHours(0, 0, 0, 0);
    endDate.setHours(23, 59, 59, 999);

    return { startDate, endDate };
}

// تصفية المبيعات حسب التاريخ
function filterSalesByDate(sales, dateRange) {
    return sales.filter(sale => {
        const saleDate = new Date(sale.timestamp || sale.date);
        return saleDate >= dateRange.startDate && saleDate <= dateRange.endDate;
    });
}

// تصفية الوصفات حسب التاريخ
function filterPrescriptionsByDate(prescriptions, dateRange) {
    return prescriptions.filter(prescription => {
        const prescriptionDate = new Date(prescription.date);
        return prescriptionDate >= dateRange.startDate && prescriptionDate <= dateRange.endDate;
    });
}

// الحصول على اسم الفئة
function getCategoryName(category) {
    const categories = {
        'medicine': 'أدوية',
        'supplements': 'مكملات غذائية',
        'cosmetics': 'مستحضرات تجميل',
        'medical_devices': 'أجهزة طبية',
        'baby_care': 'منتجات الأطفال',
        'personal_care': 'العناية الشخصية',
        'other': 'أخرى'
    };
    return categories[category] || 'غير محدد';
}

// تنسيق التاريخ والوقت
function formatDateTime(timestamp) {
    const date = new Date(timestamp);
    return date.toLocaleString('ar-SA', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    });
}

// مسح جميع البيانات
async function resetAllData() {
    try {
        const result = await Swal.fire({
            title: 'تأكيد مسح البيانات',
            text: 'هل أنت متأكد من رغبتك في مسح جميع البيانات؟ هذا الإجراء لا يمكن التراجع عنه!',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#dc3545',
            cancelButtonColor: '#6c757d',
            confirmButtonText: 'نعم، امسح البيانات',
            cancelButtonText: 'إلغاء',
            reverseButtons: true
        });

        if (result.isConfirmed) {
            showLoading(true);

            // استخدام دالة إعادة تعيين البيانات الجديدة
            const resetResult = await ipcRenderer.invoke('reset-all-data');

            if (resetResult.success) {
                // إعادة تحميل البيانات
                await loadAllData();
                updateStatisticsEnhanced();
                updateCharts();
                updateRecentSalesTable();
                updateLastUpdateTime();
                updateConnectionStatus('connected');

                showAlert('تم تصفير جميع البيانات بنجاح', 'success');
            } else {
                throw new Error(resetResult.error || 'فشل في تصفير البيانات');
            }
        }
    } catch (error) {
        console.error('خطأ في مسح البيانات:', error);
        showAlert('فشل في مسح البيانات', 'error');
    } finally {
        showLoading(false);
    }
}

// عرض حالة التحميل
function showLoading(show) {
    if (updateReportsBtn) {
        if (show) {
            updateReportsBtn.disabled = true;
            updateReportsBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري التحديث...';
        } else {
            updateReportsBtn.disabled = false;
            updateReportsBtn.innerHTML = '<i class="fas fa-sync-alt me-2"></i>تحديث التقارير';
        }
    }
}

// عرض رسائل التنبيه
function showAlert(message, type = 'info') {
    const alertTypes = {
        'success': { icon: 'success', color: '#28a745' },
        'error': { icon: 'error', color: '#dc3545' },
        'warning': { icon: 'warning', color: '#ffc107' },
        'info': { icon: 'info', color: '#17a2b8' }
    };

    const alertConfig = alertTypes[type] || alertTypes['info'];

    Swal.fire({
        icon: alertConfig.icon,
        title: message,
        showConfirmButton: false,
        timer: 3000,
        timerProgressBar: true,
        toast: true,
        position: 'top-end'
    });
}

// تصدير التقارير إلى PDF
async function exportToPDF() {
    try {
        showLoading(true);

        const dateRange = getDateRange();
        const reportData = {
            dateRange: {
                start: dateRange.startDate.toLocaleDateString('ar-SA'),
                end: dateRange.endDate.toLocaleDateString('ar-SA')
            },
            statistics: {
                totalSales: totalSalesElement.textContent,
                transactionCount: transactionCountElement.textContent,
                prescriptionCount: prescriptionCountElement.textContent,
                lowStockCount: lowStockCountElement.textContent
            },
            sales: filterSalesByDate(salesData, dateRange),
            products: productsData,
            prescriptions: filterPrescriptionsByDate(prescriptionsData, dateRange)
        };

        await ipcRenderer.invoke('export-report-pdf', reportData);
        showAlert('تم تصدير التقرير بنجاح', 'success');

    } catch (error) {
        console.error('خطأ في تصدير التقرير:', error);
        showAlert('فشل في تصدير التقرير', 'error');
    } finally {
        showLoading(false);
    }
}

// تصدير التقارير إلى Excel
async function exportToExcel() {
    try {
        showLoading(true);

        const dateRange = getDateRange();
        const reportData = {
            dateRange: {
                start: dateRange.startDate.toLocaleDateString('ar-SA'),
                end: dateRange.endDate.toLocaleDateString('ar-SA')
            },
            sales: filterSalesByDate(salesData, dateRange),
            products: productsData,
            prescriptions: filterPrescriptionsByDate(prescriptionsData, dateRange)
        };

        await ipcRenderer.invoke('export-report-excel', reportData);
        showAlert('تم تصدير التقرير بنجاح', 'success');

    } catch (error) {
        console.error('خطأ في تصدير التقرير:', error);
        showAlert('فشل في تصدير التقرير', 'error');
    } finally {
        showLoading(false);
    }
}

// طباعة التقرير
function printReport() {
    window.print();
}

// مستمع لتحديث البيانات تلقائياً
ipcRenderer.on('data-updated', async () => {
    if (autoUpdateToggle && autoUpdateToggle.checked) {
        console.log('تحديث تلقائي للتقارير...');
        await updateReports();
    }
});

// مستمع لإشعارات إتمام البيع
ipcRenderer.on('sale-completed', async (event, saleData) => {
    console.log('🎉 تم إتمام عملية بيع جديدة:', saleData);

    // تحديث فوري للتقارير دائماً
    console.log('🔄 تحديث فوري للتقارير بعد البيع...');

    // إضافة تأثير بصري للإشعار
    showSaleNotification(saleData);

    // تحديث البيانات مع تأخير قصير للتأثير البصري
    setTimeout(async () => {
        try {
            await loadAllData();
            updateStatisticsEnhanced();
            await reloadCharts(); // استخدام النظام الجديد - إعادة تحميل كاملة
            updateRecentSalesTable();
            updateLastUpdateTime();
            updateConnectionStatus('connected');
            console.log('✅ تم تحديث التقارير بعد البيع بنجاح');
        } catch (error) {
            console.error('❌ خطأ في تحديث التقارير بعد البيع:', error);
        }
    }, 1000);
});

// مستمع لإشعارات تحديث البيانات القسري
ipcRenderer.on('data-force-updated', async (event, updateData) => {
    console.log('🔄 تم تحديث البيانات قسرياً:', updateData);

    try {
        await loadAllData();
        updateStatistics();
        await reloadCharts(); // استخدام النظام الجديد
        updateRecentSalesTable();
        updateLastUpdateTime();
        updateConnectionStatus('connected');
        console.log('✅ تم تحديث التقارير بعد التحديث القسري');
    } catch (error) {
        console.error('❌ خطأ في تحديث التقارير بعد التحديث القسري:', error);
    }
});

// عرض إشعار البيع الجديد
function showSaleNotification(saleData) {
    const toast = Swal.mixin({
        toast: true,
        position: 'top-end',
        showConfirmButton: false,
        timer: 4000,
        timerProgressBar: true,
        didOpen: (toast) => {
            toast.addEventListener('mouseenter', Swal.stopTimer);
            toast.addEventListener('mouseleave', Swal.resumeTimer);
        }
    });

    toast.fire({
        icon: 'success',
        title: 'عملية بيع جديدة!',
        html: `
            <div class="text-start">
                <strong>رقم العملية:</strong> #${saleData.saleId}<br>
                <strong>المبلغ:</strong> ${saleData.saleRecord.total.toFixed(2)} ر.س<br>
                <strong>المنتجات:</strong> ${saleData.saleRecord.items.length} منتج
            </div>
        `,
        background: '#d4edda',
        color: '#155724'
    });
}

// عرض إشعار توفر تحديث
function showUpdateAvailableNotification() {
    const toast = Swal.mixin({
        toast: true,
        position: 'top-end',
        showConfirmButton: true,
        confirmButtonText: 'تحديث الآن',
        showCancelButton: true,
        cancelButtonText: 'لاحقاً',
        timer: 8000,
        timerProgressBar: true
    });

    toast.fire({
        icon: 'info',
        title: 'بيانات جديدة متوفرة',
        text: 'تم إتمام عملية بيع جديدة. هل تريد تحديث التقارير؟',
        background: '#d1ecf1',
        color: '#0c5460'
    }).then((result) => {
        if (result.isConfirmed) {
            updateReports();
        }
    });
}

// تحديث الإحصائيات مع تأثيرات بصرية
async function updateStatisticsWithAnimation() {
    // إضافة تأثير تحميل للبطاقات
    const statCards = document.querySelectorAll('.stat-card');
    statCards.forEach(card => {
        card.classList.add('loading');
    });

    // تحديث البيانات
    await loadAllData();
    updateStatistics();

    // إزالة تأثير التحميل مع تأخير
    setTimeout(() => {
        statCards.forEach(card => {
            card.classList.remove('loading');
            // إضافة تأثير نبضة للإشارة للتحديث
            card.style.animation = 'pulse 0.6s ease-in-out';
            setTimeout(() => {
                card.style.animation = '';
            }, 600);
        });
    }, 500);
}

// إضافة تأثير النبضة للـ CSS
const pulseStyle = document.createElement('style');
pulseStyle.textContent = `
    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.05); }
        100% { transform: scale(1); }
    }

    .stat-card.loading {
        opacity: 0.7;
        position: relative;
    }

    .stat-card.loading::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 20px;
        height: 20px;
        margin: -10px 0 0 -10px;
        border: 2px solid rgba(255,255,255,0.3);
        border-top: 2px solid rgba(255,255,255,0.8);
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }
`;
document.head.appendChild(pulseStyle);

// تحديث حالة الاتصال
function updateConnectionStatus(status) {
    if (!connectionStatusElement) return;

    const statusConfig = {
        'connected': {
            class: 'bg-success',
            icon: 'fas fa-wifi',
            text: 'متصل مع نقطة البيع'
        },
        'updating': {
            class: 'bg-warning',
            icon: 'fas fa-sync-alt fa-spin',
            text: 'جاري التحديث...'
        },
        'error': {
            class: 'bg-danger',
            icon: 'fas fa-exclamation-triangle',
            text: 'خطأ في الاتصال'
        },
        'disconnected': {
            class: 'bg-secondary',
            icon: 'fas fa-wifi-slash',
            text: 'غير متصل'
        }
    };

    const config = statusConfig[status] || statusConfig['disconnected'];

    connectionStatusElement.className = `badge ${config.class} me-3`;
    connectionStatusElement.innerHTML = `
        <i class="${config.icon} me-1"></i>
        ${config.text}
    `;
}

// تحديث وقت آخر تحديث
function updateLastUpdateTime() {
    if (!lastUpdateTimeElement) return;

    const now = new Date();
    const timeString = now.toLocaleTimeString('ar-SA', {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });

    lastUpdateTimeElement.textContent = timeString;

    // إضافة تأثير بصري للتحديث
    lastUpdateTimeElement.style.color = '#28a745';
    lastUpdateTimeElement.style.fontWeight = 'bold';

    setTimeout(() => {
        lastUpdateTimeElement.style.color = '';
        lastUpdateTimeElement.style.fontWeight = '';
    }, 2000);
}

// مراقبة حالة الاتصال
function monitorConnection() {
    // تحديث حالة الاتصال كل 30 ثانية
    setInterval(async () => {
        try {
            // اختبار الاتصال بالعملية الرئيسية
            await ipcRenderer.invoke('get-sales', { limit: 1 });
            updateConnectionStatus('connected');
        } catch (error) {
            console.error('فقدان الاتصال:', error);
            updateConnectionStatus('error');
        }
    }, 30000);
}

// بدء مراقبة الاتصال عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    updateConnectionStatus('connected');
    updateLastUpdateTime();
    monitorConnection();
});

// تحديث محسن للإحصائيات مع تأثيرات بصرية
async function updateStatisticsEnhanced() {
    const statCards = document.querySelectorAll('.stat-card');

    // إضافة تأثير تحميل
    statCards.forEach(card => {
        card.style.transform = 'scale(0.95)';
        card.style.opacity = '0.7';
        card.style.transition = 'all 0.3s ease';
    });

    // تحديث البيانات
    const dateRange = getDateRange();
    const filteredSales = filterSalesByDate(salesData, dateRange);

    // تحديث كل إحصائية مع تأخير تدريجي
    setTimeout(() => {
        const totalSales = filteredSales.reduce((sum, sale) => sum + sale.total, 0);
        if (totalSalesElement) {
            animateNumber(totalSalesElement, totalSales, 2);
        }
    }, 100);

    setTimeout(() => {
        if (transactionCountElement) {
            animateNumber(transactionCountElement, filteredSales.length, 0);
        }
    }, 200);

    setTimeout(() => {
        const filteredPrescriptions = filterPrescriptionsByDate(prescriptionsData, dateRange);
        if (prescriptionCountElement) {
            animateNumber(prescriptionCountElement, filteredPrescriptions.length, 0);
        }
    }, 300);

    setTimeout(() => {
        const lowStockProducts = productsData.filter(product => product.quantity < 10);
        if (lowStockCountElement) {
            animateNumber(lowStockCountElement, lowStockProducts.length, 0);
        }
    }, 400);

    // إزالة تأثير التحميل
    setTimeout(() => {
        statCards.forEach(card => {
            card.style.transform = 'scale(1)';
            card.style.opacity = '1';
        });
    }, 500);
}

// تحريك الأرقام
function animateNumber(element, targetValue, decimals = 0) {
    const startValue = parseFloat(element.textContent) || 0;
    const increment = (targetValue - startValue) / 20;
    let currentValue = startValue;

    const timer = setInterval(() => {
        currentValue += increment;

        if ((increment > 0 && currentValue >= targetValue) ||
            (increment < 0 && currentValue <= targetValue)) {
            currentValue = targetValue;
            clearInterval(timer);
        }

        element.textContent = currentValue.toFixed(decimals);
    }, 50);
}

// تصفير البيانات
async function loadDemoData() {
    try {
        const result = await Swal.fire({
            title: 'تصفير البيانات',
            html: `
                <div class="text-center">
                    <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                    <p>هل تريد تصفير جميع بيانات المبيعات والوصفات؟</p>
                    <div class="alert alert-warning mt-3">
                        <strong>تحذير:</strong> سيتم حذف جميع المبيعات والوصفات الطبية نهائياً!<br>
                        <small>ستبقى المنتجات الافتراضية فقط</small>
                    </div>
                </div>
            `,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#dc3545',
            cancelButtonColor: '#6c757d',
            confirmButtonText: '<i class="fas fa-trash-alt"></i> نعم، صفّر البيانات',
            cancelButtonText: '<i class="fas fa-times"></i> إلغاء',
            reverseButtons: true
        });

        if (result.isConfirmed) {
            // إظهار مؤشر التحميل
            Swal.fire({
                title: 'جاري التصفير...',
                html: '<i class="fas fa-spinner fa-spin fa-2x"></i><br>يتم تصفير البيانات',
                allowOutsideClick: false,
                allowEscapeKey: false,
                showConfirmButton: false
            });

            showLoading(true);
            updateConnectionStatus('updating');

            // استدعاء دالة تصفير البيانات
            const response = await ipcRenderer.invoke('force-load-demo-data');

            if (response.success) {
                // تحديث البيانات المحلية
                await loadAllData();
                updateStatisticsEnhanced();
                await reloadCharts(); // استخدام النظام الجديد
                updateRecentSalesTable();
                updateLastUpdateTime();
                updateConnectionStatus('connected');

                // عرض رسالة نجاح
                await Swal.fire({
                    title: 'تم تصفير البيانات بنجاح!',
                    html: `
                        <div class="text-center">
                            <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                            <h5>تم تصفير جميع البيانات</h5>
                            <div class="row mt-3">
                                <div class="col-4">
                                    <div class="card bg-light">
                                        <div class="card-body p-2">
                                            <small class="text-muted">المبيعات</small><br>
                                            <strong>${response.data.sales}</strong>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="card bg-light">
                                        <div class="card-body p-2">
                                            <small class="text-muted">الوصفات</small><br>
                                            <strong>${response.data.prescriptions}</strong>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="card bg-light">
                                        <div class="card-body p-2">
                                            <small class="text-muted">المنتجات</small><br>
                                            <strong>${response.data.products}</strong>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="mt-3">
                                <small class="text-success">
                                    <i class="fas fa-info-circle"></i>
                                    النظام جاهز لاستقبال البيانات الجديدة
                                </small>
                            </div>
                        </div>
                    `,
                    icon: 'success',
                    confirmButtonText: '<i class="fas fa-thumbs-up"></i> ممتاز!',
                    confirmButtonColor: '#28a745',
                    width: '500px'
                });

                console.log('✅ تم تصفير البيانات بنجاح');
            } else {
                throw new Error(response.error || 'فشل في تصفير البيانات');
            }
        }
    } catch (error) {
        console.error('❌ خطأ في تصفير البيانات:', error);
        updateConnectionStatus('error');
        showAlert('فشل في تصفير البيانات: ' + error.message, 'error');
    } finally {
        showLoading(false);
    }
}

// مستمع لإشعار التحديث القسري
ipcRenderer.on('data-force-updated', async (event, data) => {
    console.log('🔄 تم استقبال إشعار تحديث قسري للبيانات:', data);

    // تحديث فوري للتقارير
    await loadAllData();
    updateStatisticsEnhanced();
    await reloadCharts(); // استخدام النظام الجديد
    updateRecentSalesTable();
    updateLastUpdateTime();
    updateConnectionStatus('connected');

    // عرض إشعار التحديث
    const toast = Swal.mixin({
        toast: true,
        position: 'top-end',
        showConfirmButton: false,
        timer: 3000,
        timerProgressBar: true
    });

    // رسالة مختلفة حسب حالة البيانات
    const message = data.sales === 0 && data.prescriptions === 0
        ? 'تم تصفير جميع البيانات!'
        : `${data.sales} مبيعة، ${data.prescriptions} وصفة`;

    toast.fire({
        icon: data.sales === 0 && data.prescriptions === 0 ? 'info' : 'success',
        title: 'تم تحديث البيانات!',
        text: message
    });
});

// مستمع لإشعار تصفير البيانات
ipcRenderer.on('data-reset', async (event, data) => {
    console.log('🔄 تم استقبال إشعار تصفير البيانات:', data);

    // تحديث فوري للتقارير
    await loadAllData();
    updateStatisticsEnhanced();
    updateCharts();
    updateRecentSalesTable();
    updateLastUpdateTime();
    updateConnectionStatus('connected');

    // عرض إشعار التصفير
    const toast = Swal.mixin({
        toast: true,
        position: 'top-end',
        showConfirmButton: false,
        timer: 4000,
        timerProgressBar: true
    });

    toast.fire({
        icon: 'info',
        title: 'تم تصفير جميع البيانات!',
        text: 'البيانات جاهزة لبدء العمل'
    });
});

// مستمع لإشعارات تحديث المخزون
ipcRenderer.on('inventory-updated', async (event, data) => {
    console.log('🔄 تم استقبال إشعار تحديث المخزون:', data);

    // تحديث بيانات المنتجات في التقارير
    await loadAllData();
    updateStatisticsEnhanced();
    updateLastUpdateTime();

    // عرض إشعار التحديث
    const toast = Swal.mixin({
        toast: true,
        position: 'top-end',
        showConfirmButton: false,
        timer: 2000,
        timerProgressBar: true
    });

    let message = '';
    switch (data.action) {
        case 'add':
            message = `تم إضافة منتج جديد: ${data.product.name}`;
            break;
        case 'update':
            message = `تم تحديث منتج: ${data.product.name}`;
            break;
        case 'delete':
            message = `تم حذف منتج: ${data.product.name}`;
            break;
        case 'stock-add':
            message = `تم إضافة ${data.addedQuantity} قطعة إلى ${data.product.name}`;
            break;
        default:
            message = 'تم تحديث المخزون';
    }

    toast.fire({
        icon: 'info',
        title: 'تحديث المخزون',
        text: message
    });
});

// تصدير الدوال للاستخدام العام
window.reportsModule = {
    updateReports,
    resetAllData,
    loadDemoData,
    exportToPDF,
    exportToExcel,
    printReport
};
